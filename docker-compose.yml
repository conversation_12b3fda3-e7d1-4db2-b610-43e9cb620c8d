# Docker Compose 配置文件 - WR.DO 应用部署
services:
  # PostgreSQL 数据库服务
  postgres:
    image: postgres:16-alpine
    container_name: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=wrdo
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - wrdo-network
    restart: unless-stopped

  # 主应用服务
  app:
    # 使用 GitHub Container Registry 中的镜像，TAG 默认为 main
    image: ghcr.io/oiov/wr.do/wrdo:${TAG:-main}
    # 容器名称
    container_name: wrdo
    # 端口映射：主机端口3000映射到容器端口3000
    ports:
      - "3000:3000"
    # 环境变量配置
    environment:
      # Node.js 运行环境设置为生产模式
      NODE_ENV: production
      # 数据库连接URL
      DATABASE_URL: ${DATABASE_URL}
      # 身份验证密钥，默认值为 your-auth-secret
      AUTH_SECRET: ${AUTH_SECRET:-your-auth-secret}
      # 身份验证服务URL
      AUTH_URL: ${AUTH_URL}
      # 应用公开访问URL
      NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
      # Google OAuth 客户端ID
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      # Google OAuth 客户端密钥
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      # GitHub OAuth 应用ID
      GITHUB_ID: ${GITHUB_ID}
      # GitHub OAuth 应用密钥
      GITHUB_SECRET: ${GITHUB_SECRET}
      # LinuxDo OAuth 客户端ID
      LinuxDo_CLIENT_ID: ${LinuxDo_CLIENT_ID}
      # LinuxDo OAuth 客户端密钥
      LinuxDo_CLIENT_SECRET: ${LinuxDo_CLIENT_SECRET}
      # Resend 邮件服务API密钥
      RESEND_API_KEY: ${RESEND_API_KEY}
      # 发送邮件的发件人地址
      RESEND_FROM_EMAIL: ${RESEND_FROM_EMAIL}
      # 邮件R2域名配置
      NEXT_PUBLIC_EMAIL_R2_DOMAIN: ${NEXT_PUBLIC_EMAIL_R2_DOMAIN}
      # Google Analytics ID
      NEXT_PUBLIC_GOOGLE_ID: ${NEXT_PUBLIC_GOOGLE_ID}
      # 截图服务基础URL
      SCREENSHOTONE_BASE_URL: ${SCREENSHOTONE_BASE_URL}
      # GitHub API访问令牌
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      # 是否跳过数据库检查
      SKIP_DB_CHECK: ${SKIP_DB_CHECK}
      # 是否跳过数据库迁移
      SKIP_DB_MIGRATION: ${SKIP_DB_MIGRATION}
    # 网络配置
    networks:
      - wrdo-network
    # 重启策略：除非手动停止，否则总是重启
    restart: unless-stopped

# 网络配置
networks:
  # 自定义网络：wrdo-network
  wrdo-network:
    # 使用桥接驱动
    driver: bridge

# 数据卷配置
volumes:
  postgres-data:
    name: wrdo-postgres-data
    